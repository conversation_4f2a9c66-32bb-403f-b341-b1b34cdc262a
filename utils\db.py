import cx_Oracle

# 连接配置
ORACLE_CONFIG = {
    "host": "mesall.cn",
    "port": 33901,
    "user": "BIZ_DATA_FETCH",
    "password": "4ZBWSuCdPQJN~vhCt2nkJy1_Ufxh5b",
    "service_name": "orcl"
}

def get_connection():
    """获取 Oracle 数据库连接"""
    # 设置 NLS_LANG 环境变量
    import os
    os.environ["NLS_LANG"] = ".AL32UTF8"
    
    dsn = cx_Oracle.makedsn(
        ORACLE_CONFIG["host"],
        ORACLE_CONFIG["port"],
        service_name=ORACLE_CONFIG["service_name"]
    )
    conn = cx_Oracle.connect(
        user=OR<PERSON><PERSON>_CONFIG["user"],
        password=ORACLE_CONFIG["password"],
        dsn=dsn,
        encoding="UTF-8",
        nencoding="UTF-8"
    )
    return conn
