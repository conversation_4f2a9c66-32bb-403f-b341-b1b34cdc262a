import http.client
from bs4 import BeautifulSoup

# 1. 发起请求
conn = http.client.HTTPSConnection("www.tandfonline.com")
payload = ''
headers = {
    'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
    'Accept': '*/*',
    'Host': 'www.tandfonline.com',
    'Connection': 'keep-alive',
    'Cookie': 'MAID=ljayeMBcOyncAWx1O1Z/4Q==; MACHINE_LAST_SEEN=2025-08-21T02%3A06%3A30.777-07%3A00; JSESSIONID=4465698636444CDCC5B3C7357554243F'
}
conn.request("GET", "/doi/full/10.1080/19440049.2025.2544327", payload, headers)
res = conn.getresponse()
html = res.read().decode("utf-8")

# 2. 解析 HTML
soup = BeautifulSoup(html, "html.parser")

# 3. 提取 abstract 内容
abstract_div = soup.find("div", id="abstractId1")
if abstract_div:
    paragraph = abstract_div.find("p", class_="last")
    if paragraph:
        abstract_text = paragraph.get_text(strip=True)
        print(abstract_text)
