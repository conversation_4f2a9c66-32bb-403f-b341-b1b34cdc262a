from DrissionPage import Chromium,ChromiumOptions
import time

def extract_article_info(tab):
    """
    抓取网页中的文章信息
    提取标题、日期、链接
    """
    articles_data = []

    # 等待页面加载完成
    time.sleep(2)

    # 查找所有文章元素 - 使用div.articleEntry选择器
    articles = tab.eles('div.articleEntry')
    print(f"找到 {len(articles)} 篇文章")

    for i, article in enumerate(articles, 1):
        try:
            print(f"正在处理第 {i} 篇文章...")
            article_info = {}

            # 1. 抓取标题和链接 - 从.art_title .ref a标签中获取
            title_element = article.ele('.art_title .ref', timeout=0.5)
            if title_element:
                # 获取标题文本
                title_span = title_element.ele('.hlFld-Title', timeout=0.5)
                if title_span:
                    article_info['title'] = title_span.text.strip()
                else:
                    article_info['title'] = title_element.text.strip()

                # 获取链接
                article_info['link'] = title_element.attr('href')
                # 如果链接是相对路径，补全为绝对路径
                if article_info['link'] and article_info['link'].startswith('/'):
                    article_info['link'] = 'https://www.tandfonline.com' + article_info['link']

                print(f"  找到标题: {article_info['title']}")
                print(f"  找到链接: {article_info['link']}")
            else:
                article_info['title'] = "未找到标题"
                article_info['link'] = ""
                print(f"  未找到标题元素")

            # 2. 抓取发布日期 - 从.tocEPubDate .date中获取
            date_element = article.ele('.tocEPubDate .date', timeout=0.5)
            if date_element:
                article_info['date'] = date_element.text.strip()
                print(f"  找到日期: {article_info['date']}")
            else:
                # 备用方案：查找其他可能的日期元素
                date_element = article.ele('[data-pub-date]', timeout=0.5)
                if date_element:
                    # 从data-pub-date属性获取时间戳并转换
                    timestamp = date_element.attr('data-pub-date')
                    if timestamp:
                        try:
                            import datetime
                            date_obj = datetime.datetime.fromtimestamp(int(timestamp) / 1000)
                            article_info['date'] = date_obj.strftime('%d %b %Y')
                            print(f"  从时间戳获取日期: {article_info['date']}")
                        except:
                            article_info['date'] = "未找到日期"
                    else:
                        article_info['date'] = "未找到日期"
                else:
                    article_info['date'] = "未找到日期"
                    print(f"  未找到日期元素")

            # 3. 抓取作者信息（可选）
            authors_elements = article.eles('.tocAuthors .entryAuthor')
            if authors_elements:
                authors = [author.text.strip() for author in authors_elements]
                article_info['authors'] = ', '.join(authors)
                print(f"  找到作者: {article_info['authors']}")
            else:
                article_info['authors'] = "未找到作者"

            articles_data.append(article_info)

        except Exception as e:
            print(f"抓取第 {i} 篇文章时出错: {e}")
            continue

    return articles_data

def print_article_info(articles):
    """
    格式化打印文章信息
    """
    for i, article in enumerate(articles, 1):
        print(f"\n{'='*80}")
        print(f"文章 {i}")
        print(f"{'='*80}")
        print(f"标题: {article['title']}")
        print(f"日期: {article['date']}")
        print(f"作者: {article['authors']}")
        print(f"链接: {article['link']}")

def main():
    """
    主函数
    """
    # 创建浏览器实例，启用无头模式提高速度
    co = ChromiumOptions()
    # co.headless()
    browser = Chromium(co)
    tab = browser.latest_tab

    try:
        # 访问目标网页
        print("正在访问网页...")
        start_time = time.time()
        tab.get('https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20')
        print(f"页面加载耗时: {time.time() - start_time:.2f}秒")

        # 抓取文章信息
        print("正在抓取文章信息...")
        extract_start = time.time()
        articles = extract_article_info(tab)
        print(f"信息抓取耗时: {time.time() - extract_start:.2f}秒")

        # 打印结果
        print(f"\n共找到 {len(articles)} 篇文章")
        print_article_info(articles)

        print(f"\n总耗时: {time.time() - start_time:.2f}秒")

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        # 自动关闭浏览器
        print("\n抓取完成，正在关闭浏览器...")
        browser.quit()

if __name__ == "__main__":
    main()