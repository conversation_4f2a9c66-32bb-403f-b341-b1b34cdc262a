from DrissionPage import Chromium,ChromiumOptions
from bs4 import BeautifulSoup
import time
import http.client
import urllib.parse

def get_article_abstract(article_url, max_retries=3, timeout=30):
    """根据文章链接获取摘要，支持重试和超时控制"""
    for attempt in range(max_retries):
        try:
            # 解析URL
            parsed_url = urllib.parse.urlparse(article_url)
            host = parsed_url.netloc
            path = parsed_url.path

            # 建立HTTPS连接，设置超时
            conn = http.client.HTTPSConnection(host, timeout=timeout)

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # 发送GET请求
            conn.request("GET", path, '', headers)

            # 等待响应，设置超时
            res = conn.getresponse()

            # 检查响应状态
            if res.status != 200:
                conn.close()
                if attempt < max_retries - 1:
                    wait_time = 5 * (attempt + 1)
                    time.sleep(wait_time)
                    continue
                else:
                    return f"HTTP错误: {res.status}"

            html = res.read().decode("utf-8")
            conn.close()

            # 解析HTML
            soup = BeautifulSoup(html, "html.parser")

            # 提取abstract内容 - 根据你提供的HTML结构
            abstract_div = soup.find("div", id="abstractId1")
            if abstract_div:
                # 查找class为"last"的p标签
                paragraph = abstract_div.find("p", class_="last")
                if paragraph:
                    abstract_text = paragraph.get_text(strip=True)
                    return abstract_text
                else:
                    # 如果没有找到class="last"的p标签，查找所有p标签
                    paragraphs = abstract_div.find_all("p")
                    if paragraphs:
                        abstract_text = paragraphs[-1].get_text(strip=True)  # 取最后一个p标签
                        return abstract_text

            # 如果没有找到abstractId1，尝试其他选择器
            abstract_section = soup.find("section", class_="abstract")
            if abstract_section:
                abstract_p = abstract_section.find("p")
                if abstract_p:
                    abstract_text = abstract_p.get_text(strip=True)
                    return abstract_text

            # 备用方案：查找包含"Abstract"的div
            abstract_divs = soup.find_all("div", class_=lambda x: x and "abstract" in x.lower())
            for div in abstract_divs:
                p_tags = div.find_all("p")
                if p_tags:
                    abstract_text = p_tags[-1].get_text(strip=True)
                    if len(abstract_text) > 50:  # 确保是有意义的摘要
                        return abstract_text

            return "未找到摘要"

        except (http.client.HTTPException, ConnectionError, TimeoutError) as e:
            if attempt < max_retries - 1:
                wait_time = 10 * (attempt + 1)  # 网络错误等待更长时间
                time.sleep(wait_time)
            else:
                return "网络连接失败"
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 5 * (attempt + 1)
                time.sleep(wait_time)
            else:
                return f"获取摘要失败: {str(e)}"

    return "重试次数已用完"

def extract_article_info(tab):
    """
    抓取网页中的文章信息
    提取标题、日期、链接
    """
    articles_data = []

    # 等待页面加载完成
    time.sleep(2)

    # 获取页面HTML源码并用BeautifulSoup解析
    html_source = tab.html
    soup = BeautifulSoup(html_source, 'html.parser')

    # 查找所有文章元素 - 参考selenium版本的逻辑
    article_entries = soup.find_all('div', class_='articleEntry')

    if not article_entries:
        # 尝试查找其他可能的文章容器
        article_entries = soup.find_all('div', class_='tocArticleEntry')
        if article_entries:
            print("使用备用选择器找到文章")

    print(f"找到 {len(article_entries)} 篇文章")

    for i, article_element in enumerate(article_entries, 1):
        try:
            print(f"正在处理第 {i} 篇文章...")
            article_info = {}

            # 1. 抓取标题和链接 - 完全参考selenium版本的逻辑
            title_link = article_element.find('div', class_='art_title linkable')
            if not title_link:
                print(f"  未找到标题容器")
                continue

            title_a = title_link.find('a', class_='ref nowrap')
            if not title_a:
                print(f"  未找到标题链接")
                continue

            title_span = title_a.find('span', class_='hlFld-Title')
            if title_span:
                title = title_span.get_text(strip=True)
            else:
                title = title_a.get_text(strip=True)

            article_url = title_a.get('href', '')
            if article_url and not article_url.startswith('http'):
                article_url = f"https://www.tandfonline.com{article_url}"

            article_info['title'] = title
            article_info['link'] = article_url

            print(f"  找到标题: {title}")
            print(f"  找到链接: {article_url}")

            # 2. 抓取作者信息 - 完全参考selenium版本的逻辑
            authors = []
            authors_div = article_element.find('div', class_='tocAuthors articleEntryAuthor')
            if authors_div:
                author_links = authors_div.find_all('a', class_='entryAuthor linkable hlFld-ContribAuthor')
                for author_link in author_links:
                    author_name = author_link.get_text(strip=True)
                    if author_name:
                        authors.append(author_name)

            authors_str = ', '.join(authors) if authors else '未找到作者'
            article_info['authors'] = authors_str
            print(f"  找到作者: {authors_str}")

            # 3. 抓取发布日期 - 完全参考selenium版本的逻辑
            pub_date = ''
            date_div = article_element.find('div', class_='tocEPubDate')
            if date_div:
                date_span = date_div.find('span', class_='date')
                if date_span:
                    pub_date = date_span.get_text(strip=True)
                else:
                    # 如果没有找到span.date，尝试获取整个div的文本并解析
                    date_text = date_div.get_text(strip=True)
                    if "Published online:" in date_text:
                        pub_date = date_text.split("Published online:")[-1].strip()
                    else:
                        pub_date = date_text
            else:
                pub_date = '未找到日期'

            article_info['date'] = pub_date
            print(f"  找到日期: {pub_date}")

            # 4. 获取摘要
            if article_info['link']:
                print(f"  正在获取摘要...")
                abstract = get_article_abstract(article_info['link'])
                article_info['abstract'] = abstract
                print(f"  摘要获取结果: {abstract[:100]}..." if len(abstract) > 100 else f"  摘要: {abstract}")

                # 根据结果调整等待时间
                if "失败" in abstract or "错误" in abstract or "重试" in abstract:
                    time.sleep(8)  # 失败后等待更长时间
                else:
                    time.sleep(5)  # 成功后等待时间
            else:
                article_info['abstract'] = "无有效链接"

            articles_data.append(article_info)

        except Exception as e:
            print(f"抓取第 {i} 篇文章时出错: {e}")
            continue

    return articles_data

def print_article_info(articles):
    """
    格式化打印文章信息
    """
    for i, article in enumerate(articles, 1):
        print(f"\n{'='*80}")
        print(f"文章 {i}")
        print(f"{'='*80}")
        print(f"标题: {article['title']}")
        print(f"日期: {article['date']}")
        print(f"作者: {article['authors']}")
        print(f"链接: {article['link']}")
        print(f"摘要: {article.get('abstract', '未获取')}")
        print("-" * 80)

def main():
    """
    主函数
    """
    # 创建浏览器实例
    co = ChromiumOptions()
    # co.headless()  # 可以启用无头模式
    browser = Chromium(co)
    tab = browser.latest_tab

    try:
        # 访问目标网页
        print("正在访问网页...")
        start_time = time.time()
        tab.get('https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20')
        print(f"页面加载耗时: {time.time() - start_time:.2f}秒")

        # 抓取文章信息
        print("正在抓取文章信息...")
        extract_start = time.time()
        articles = extract_article_info(tab)
        print(f"信息抓取耗时: {time.time() - extract_start:.2f}秒")

        # 打印结果
        print(f"\n共找到 {len(articles)} 篇文章")
        print_article_info(articles)

        print(f"\n总耗时: {time.time() - start_time:.2f}秒")

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        # 自动关闭浏览器
        print("\n抓取完成，正在关闭浏览器...")
        browser.quit()

if __name__ == "__main__":
    main()