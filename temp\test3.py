from DrissionPage import Chromium,ChromiumOptions
from bs4 import BeautifulSoup
import time

def get_article_abstract(tab, article_url):
    """使用DrissionPage浏览器获取文章摘要"""
    try:
        print(f"    正在访问文章页面: {article_url}")

        # 使用浏览器访问文章页面
        tab.get(article_url)
        time.sleep(3)  # 等待页面加载

        # 获取页面HTML源码并用BeautifulSoup解析
        html_source = tab.html
        soup = BeautifulSoup(html_source, 'html.parser')

        # 提取abstract内容 - 根据你提供的HTML结构
        abstract_div = soup.find("div", id="abstractId1")
        if abstract_div:
            # 查找class为"last"的p标签
            paragraph = abstract_div.find("p", class_="last")
            if paragraph:
                abstract_text = paragraph.get_text(strip=True)
                print(f"    成功获取摘要，长度: {len(abstract_text)}")
                return abstract_text
            else:
                # 如果没有找到class="last"的p标签，查找所有p标签
                paragraphs = abstract_div.find_all("p")
                if paragraphs:
                    abstract_text = paragraphs[-1].get_text(strip=True)  # 取最后一个p标签
                    print(f"    备用方案获取摘要，长度: {len(abstract_text)}")
                    return abstract_text

        # 如果没有找到abstractId1，尝试其他选择器
        abstract_section = soup.find("section", class_="abstract")
        if abstract_section:
            abstract_p = abstract_section.find("p")
            if abstract_p:
                abstract_text = abstract_p.get_text(strip=True)
                print(f"    备用方案2获取摘要，长度: {len(abstract_text)}")
                return abstract_text

        # 备用方案：查找包含"Abstract"的div
        abstract_divs = soup.find_all("div", class_=lambda x: x and "abstract" in x.lower())
        for div in abstract_divs:
            p_tags = div.find_all("p")
            if p_tags:
                abstract_text = p_tags[-1].get_text(strip=True)
                if len(abstract_text) > 50:  # 确保是有意义的摘要
                    print(f"    备用方案3获取摘要，长度: {len(abstract_text)}")
                    return abstract_text

        print(f"    未找到摘要内容")
        return "未找到摘要"

    except Exception as e:
        print(f"    获取摘要时出错: {e}")
        return f"获取摘要失败: {str(e)}"

def extract_article_info(tab):
    """
    抓取网页中的文章信息
    提取标题、日期、链接
    """
    articles_data = []

    # 等待页面加载完成
    time.sleep(2)

    # 获取页面HTML源码并用BeautifulSoup解析
    html_source = tab.html
    soup = BeautifulSoup(html_source, 'html.parser')

    # 查找所有文章元素 - 参考selenium版本的逻辑
    article_entries = soup.find_all('div', class_='articleEntry')

    if not article_entries:
        # 尝试查找其他可能的文章容器
        article_entries = soup.find_all('div', class_='tocArticleEntry')
        if article_entries:
            print("使用备用选择器找到文章")

    print(f"找到 {len(article_entries)} 篇文章")

    for i, article_element in enumerate(article_entries, 1):
        try:
            print(f"正在处理第 {i} 篇文章...")
            article_info = {}

            # 1. 抓取标题和链接 - 完全参考selenium版本的逻辑
            title_link = article_element.find('div', class_='art_title linkable')
            if not title_link:
                print(f"  未找到标题容器")
                continue

            title_a = title_link.find('a', class_='ref nowrap')
            if not title_a:
                print(f"  未找到标题链接")
                continue

            title_span = title_a.find('span', class_='hlFld-Title')
            if title_span:
                title = title_span.get_text(strip=True)
            else:
                title = title_a.get_text(strip=True)

            article_url = title_a.get('href', '')
            if article_url and not article_url.startswith('http'):
                article_url = f"https://www.tandfonline.com{article_url}"

            article_info['title'] = title
            article_info['link'] = article_url

            print(f"  找到标题: {title}")
            print(f"  找到链接: {article_url}")

            # 2. 抓取作者信息 - 完全参考selenium版本的逻辑
            authors = []
            authors_div = article_element.find('div', class_='tocAuthors articleEntryAuthor')
            if authors_div:
                author_links = authors_div.find_all('a', class_='entryAuthor linkable hlFld-ContribAuthor')
                for author_link in author_links:
                    author_name = author_link.get_text(strip=True)
                    if author_name:
                        authors.append(author_name)

            authors_str = ', '.join(authors) if authors else '未找到作者'
            article_info['authors'] = authors_str
            print(f"  找到作者: {authors_str}")

            # 3. 抓取发布日期 - 完全参考selenium版本的逻辑
            pub_date = ''
            date_div = article_element.find('div', class_='tocEPubDate')
            if date_div:
                date_span = date_div.find('span', class_='date')
                if date_span:
                    pub_date = date_span.get_text(strip=True)
                else:
                    # 如果没有找到span.date，尝试获取整个div的文本并解析
                    date_text = date_div.get_text(strip=True)
                    if "Published online:" in date_text:
                        pub_date = date_text.split("Published online:")[-1].strip()
                    else:
                        pub_date = date_text
            else:
                pub_date = '未找到日期'

            article_info['date'] = pub_date
            print(f"  找到日期: {pub_date}")

            # 暂时不获取摘要，先收集所有文章信息
            article_info['abstract'] = ""  # 初始化为空，后续获取

            articles_data.append(article_info)

        except Exception as e:
            print(f"抓取第 {i} 篇文章时出错: {e}")
            continue

    return articles_data

def print_article_info(articles):
    """
    格式化打印文章信息
    """
    for i, article in enumerate(articles, 1):
        print(f"\n{'='*80}")
        print(f"文章 {i}")
        print(f"{'='*80}")
        print(f"标题: {article['title']}")
        print(f"日期: {article['date']}")
        print(f"作者: {article['authors']}")
        print(f"链接: {article['link']}")
        print(f"摘要: {article.get('abstract', '未获取')}")
        print("-" * 80)

def main():
    """
    主函数
    """
    # 创建浏览器实例
    co = ChromiumOptions()
    # co.headless()  # 可以启用无头模式
    browser = Chromium(co)
    tab = browser.latest_tab

    try:
        # 访问目标网页
        print("正在访问网页...")
        start_time = time.time()
        tab.get('https://www.tandfonline.com/action/showAxaArticles?journalCode=tfac20')
        print(f"页面加载耗时: {time.time() - start_time:.2f}秒")

        # 抓取文章信息
        print("正在抓取文章信息...")
        extract_start = time.time()
        articles = extract_article_info(tab)
        print(f"信息抓取耗时: {time.time() - extract_start:.2f}秒")

        # 获取每篇文章的摘要
        if articles:
            print(f"\n开始获取 {len(articles)} 篇文章的摘要...")
            for i, article in enumerate(articles, 1):
                print(f"\n正在获取第 {i}/{len(articles)} 篇文章的摘要...")

                if article['link']:
                    abstract = get_article_abstract(tab, article['link'])
                    article['abstract'] = abstract

                    # 根据结果调整等待时间
                    if "失败" in abstract or "错误" in abstract:
                        time.sleep(8)  # 失败后等待更长时间
                    else:
                        time.sleep(5)  # 成功后等待时间
                else:
                    article['abstract'] = "无有效链接"

        # 打印结果
        print(f"\n共找到 {len(articles)} 篇文章")
        print_article_info(articles)

        print(f"\n总耗时: {time.time() - start_time:.2f}秒")

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        # 自动关闭浏览器
        print("\n抓取完成，正在关闭浏览器...")
        browser.quit()

if __name__ == "__main__":
    main()