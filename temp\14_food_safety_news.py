from DrissionPage import Chromium, ChromiumOptions
import time


def extract_article_info(tab):
    """
    抓取网页中的文章信息
    只提取标题、内容、时间、跳转链接
    """
    articles_data = []

    # 等待页面加载完成
    time.sleep(1)

    # 查找所有文章元素
    articles = tab.eles('tag:article')

    for i, article in enumerate(articles, 1):
        try:
            article_info = {}

            # 1. 抓取标题和链接
            title_element = article.ele('.lxb_af-template_tags-get_linked_post_title-link', timeout=0.5)
            if title_element:
                article_info['title'] = title_element.text.strip()
                article_info['link'] = title_element.attr('href')
            else:
                article_info['title'] = "未找到标题"
                article_info['link'] = ""

            # 2. 抓取时间
            time_element = article.ele('tag:time', timeout=0.5)
            if time_element:
                article_info['time'] = time_element.text.strip()
            else:
                article_info['time'] = "未找到时间"

            # 3. 抓取内容摘要
            content_div = None
            selectors = [
                '.lxb_af-post_content.lxb_af-clear',
                '.lxb_af-post_content',
                '[class*="lxb_af-post_content"]'
            ]

            for selector in selectors:
                content_div = article.ele(selector, timeout=0.5)
                if content_div:
                    break

            if content_div:
                # 获取所有段落元素
                paragraphs = content_div.eles('tag:p', timeout=0.5)
                content_parts = []

                for p in paragraphs:
                    p_text = p.text.strip()
                    if p_text:
                        content_parts.append(p_text)

                # 合并所有段落内容
                if content_parts:
                    article_info['content'] = '\n\n'.join(content_parts)
                else:
                    # 获取整个div的文本
                    full_text = content_div.text.strip()
                    if "Continue Reading" in full_text:
                        full_text = full_text.split("Continue Reading")[0].strip()
                    article_info['content'] = full_text
            else:
                # 备用方案：查找包含post_content的div
                all_divs = article.eles('tag:div', timeout=0.5)
                for div in all_divs:
                    div_class = div.attr('class') or ''
                    if 'post_content' in div_class:
                        content_text = div.text.strip()
                        if content_text and len(content_text) > 50:
                            if "Continue Reading" in content_text:
                                content_text = content_text.split("Continue Reading")[0].strip()
                            article_info['content'] = content_text
                            break
                else:
                    article_info['content'] = "未找到内容"

            articles_data.append(article_info)

        except Exception as e:
            print(f"抓取第 {i} 篇文章时出错: {e}")
            continue

    return articles_data


def print_article_info(articles):
    """
    格式化打印文章信息
    """
    for i, article in enumerate(articles, 1):
        print(f"\n{'=' * 60}")
        print(f"文章 {i}")
        print(f"{'=' * 60}")
        print(f"标题: {article['title']}")
        print(f"时间: {article['time']}")
        print(f"内容: {article['content']}")
        print(f"跳转链接: {article['link']}")


def main():
    """
    主函数
    """
    # 创建浏览器实例
    co = ChromiumOptions()
    # co.headless()  # 取消注释可启用无头模式
    browser = Chromium(co)
    tab = browser.latest_tab

    try:
        # 访问目标网页
        print("正在访问网页...")
        tab.get('https://www.foodsafetynews.com/sections/food-recalls/')

        # 抓取文章信息
        print("正在抓取文章信息...")
        articles = extract_article_info(tab)

        # 打印结果
        print(f"\n共找到 {len(articles)} 篇文章")
        print_article_info(articles)

    except Exception as e:
        print(f"程序执行出错: {e}")

    finally:
        # 自动关闭浏览器
        print("\n抓取完成，正在关闭浏览器...")
        browser.quit()


if __name__ == "__main__":
    main()
